const fetch = require('node-fetch');

// Konfigurasi test
const BASE_URL = 'http://localhost:3000';
let authToken = '';

// <PERSON>gs<PERSON> helper untuk login
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>', // Ganti dengan email yang valid
        password: 'password123'    // Ganti dengan password yang valid
      })
    });

    const result = await response.json();
    if (response.ok) {
      authToken = result.token;
      console.log('✅ Login berhasil');
      return true;
    } else {
      console.log('❌ Login gagal:', result.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Error login:', error.message);
    return false;
  }
}

// Fungsi untuk mendapatkan data debug
async function getDebugData() {
  try {
    const response = await fetch(`${BASE_URL}/booking/debug`);
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Debug data berhasil diambil');
      console.log('Recent bookings:', result.data.recent_bookings);
      console.log('Sample products:', result.data.sample_products);
      return result.data;
    } else {
      console.log('❌ Gagal mengambil debug data:', result.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Error debug data:', error.message);
    return null;
  }
}

// Test multiple activities dengan data yang valid
async function testMultipleActivities(bookingId, productIds) {
  try {
    const testData = {
      booking_id: bookingId,
      activity: productIds.map((id, index) => ({
        id: id,
        qty: index + 1 // qty 1, 2, 3, dst
      }))
    };

    console.log('\n🧪 Testing multiple activities dengan data:');
    console.log(JSON.stringify(testData, null, 2));

    const response = await fetch(`${BASE_URL}/booking_activities`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Multiple activities berhasil ditambahkan!');
      console.log('Response:', JSON.stringify(result, null, 2));
      return result;
    } else {
      console.log('❌ Gagal menambahkan multiple activities:');
      console.log('Status:', response.status);
      console.log('Error:', JSON.stringify(result, null, 2));
      return null;
    }
  } catch (error) {
    console.log('❌ Error testing multiple activities:', error.message);
    return null;
  }
}

// Test single activity untuk perbandingan
async function testSingleActivity(bookingId, productId) {
  try {
    const testData = {
      activity: productId,
      qty: 1
    };

    console.log('\n🧪 Testing single activity dengan data:');
    console.log(JSON.stringify(testData, null, 2));

    const response = await fetch(`${BASE_URL}/booking/${bookingId}/activities`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Single activity berhasil ditambahkan!');
      console.log('Response:', JSON.stringify(result, null, 2));
      return result;
    } else {
      console.log('❌ Gagal menambahkan single activity:');
      console.log('Status:', response.status);
      console.log('Error:', JSON.stringify(result, null, 2));
      return null;
    }
  } catch (error) {
    console.log('❌ Error testing single activity:', error.message);
    return null;
  }
}

// Test get activities
async function testGetActivities(bookingId) {
  try {
    console.log('\n🧪 Testing get activities untuk booking:', bookingId);

    const response = await fetch(`${BASE_URL}/booking/${bookingId}/activities`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Get activities berhasil!');
      console.log('Activities count:', result.data.length);
      console.log('Activities:', JSON.stringify(result.data, null, 2));
      return result;
    } else {
      console.log('❌ Gagal get activities:');
      console.log('Status:', response.status);
      console.log('Error:', JSON.stringify(result, null, 2));
      return null;
    }
  } catch (error) {
    console.log('❌ Error testing get activities:', error.message);
    return null;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Memulai test booking activities yang sudah diperbaiki...\n');

  // 1. Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ Test dihentikan karena login gagal');
    return;
  }

  // 2. Get debug data
  const debugData = await getDebugData();
  if (!debugData || !debugData.recent_bookings.length || !debugData.sample_products.length) {
    console.log('❌ Test dihentikan karena tidak ada data booking atau product');
    return;
  }

  // 3. Ambil booking dan product pertama untuk test
  const testBooking = debugData.recent_bookings[0];
  const testProducts = debugData.sample_products.slice(0, 3); // Ambil 3 product pertama

  console.log('\n📋 Data test:');
  console.log('Booking ID:', testBooking.booking_id);
  console.log('Booking Record ID:', testBooking.id);
  console.log('Products:', testProducts.map(p => ({ id: p.id, nama: p.nama_layanan })));

  // 4. Test dengan booking_id (field)
  console.log('\n=== TEST 1: Multiple Activities dengan booking_id (field) ===');
  await testMultipleActivities(testBooking.booking_id, testProducts.map(p => p.id));

  // 5. Test dengan record ID
  console.log('\n=== TEST 2: Multiple Activities dengan record ID ===');
  await testMultipleActivities(testBooking.id, testProducts.map(p => p.id));

  // 6. Test single activity untuk perbandingan
  console.log('\n=== TEST 3: Single Activity dengan booking_id (field) ===');
  await testSingleActivity(testBooking.booking_id, testProducts[0].id);

  // 7. Test single activity dengan record ID
  console.log('\n=== TEST 4: Single Activity dengan record ID ===');
  await testSingleActivity(testBooking.id, testProducts[0].id);

  // 8. Test get activities
  console.log('\n=== TEST 5: Get Activities dengan booking_id (field) ===');
  await testGetActivities(testBooking.booking_id);

  console.log('\n=== TEST 6: Get Activities dengan record ID ===');
  await testGetActivities(testBooking.id);

  console.log('\n🎉 Semua test selesai!');
}

// Jalankan test
runTests().catch(console.error);
