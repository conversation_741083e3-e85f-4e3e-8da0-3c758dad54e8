# Troubleshooting Guide: Error "Gagal menambahkan aktivitas ke booking"

## 🔍 Langkah-langkah Debugging

### 1. Cek Data yang Tersedia
Pertama, gunakan endpoint debug untuk melihat data yang tersedia:

```bash
GET http://localhost:3000/booking/debug
```

Ini akan menampilkan:
- 5 booking terbaru dengan ID yang valid
- 5 produk terbaru dengan ID yang valid

### 2. Cek Authentication
Pastikan Anda menggunakan token yang valid:

```bash
# Login dulu untuk mendapatkan token
POST http://localhost:3000/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

Response akan memberikan token yang harus digunakan di header.

### 3. Test dengan Data Valid
Gunakan data dari endpoint debug untuk testing:

```bash
POST http://localhost:3000/booking_activities
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "booking_id": "ID_DARI_DEBUG_ENDPOINT",
  "activity": [
    {
      "id": "PRODUCT_ID_DARI_DEBUG_ENDPOINT",
      "qty": 1
    }
  ]
}
```

## 🚨 Kemungkinan Penyebab Error

### 1. **Authentication Error (401)**
**Penyebab:** Token tidak ada atau tidak valid
**Solusi:** 
- Login ulang untuk mendapatkan token baru
- Pastikan header `Authorization: Bearer <token>` ada

### 2. **Booking Not Found (404)**
**Penyebab:** `booking_id` tidak ada di database
**Solusi:**
- Gunakan endpoint debug untuk mendapatkan booking_id yang valid
- Pastikan menggunakan ID dari PocketBase (bukan format BK-YYYYMMDD-XXXXX)

### 3. **Invalid Activity ID (500)**
**Penyebab:** `activity.id` tidak ada di collection `product_uab`
**Solusi:**
- Gunakan endpoint debug untuk mendapatkan product_id yang valid
- Pastikan product dengan ID tersebut benar-benar ada

### 4. **Validation Error (400)**
**Penyebab:** Format request tidak sesuai
**Solusi:**
- Pastikan `booking_id` tidak kosong
- Pastikan `activity` adalah array dan tidak kosong
- Pastikan setiap activity memiliki `id` dan `qty`
- Pastikan `qty` > 0

### 5. **Database Connection Error (500)**
**Penyebab:** Masalah koneksi ke PocketBase
**Solusi:**
- Pastikan PocketBase server berjalan
- Cek konfigurasi koneksi di `.env`

## 🛠️ Format Request yang Benar

```json
{
  "booking_id": "RECORD_ID_DARI_POCKETBASE",
  "activity": [
    {
      "id": "PRODUCT_ID_DARI_POCKETBASE",
      "qty": 2
    },
    {
      "id": "PRODUCT_ID_LAIN_DARI_POCKETBASE",
      "qty": 1
    }
  ]
}
```

## 📝 Contoh Debugging dengan cURL

```bash
# 1. Cek data yang tersedia
curl -X GET http://localhost:3000/booking/debug

# 2. Login untuk mendapatkan token
curl -X POST http://localhost:3000/login \
  -H "Content-Type: application/json" \
  -d '{"email":"your_email","password":"your_password"}'

# 3. Test dengan data valid
curl -X POST http://localhost:3000/booking_activities \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "booking_id": "VALID_BOOKING_ID",
    "activity": [
      {
        "id": "VALID_PRODUCT_ID",
        "qty": 1
      }
    ]
  }'
```

## 🔧 Logging untuk Debug

Server sekarang akan menampilkan log detail di console:
- Request body yang diterima
- User ID dari token
- Status pencarian booking
- Detail pembuatan setiap activity
- Error detail jika ada masalah

Periksa console server untuk melihat log error yang lebih detail.

## 📞 Jika Masih Error

Jika masih mengalami error setelah mengikuti langkah di atas:

1. **Cek log server** di console untuk error detail
2. **Gunakan endpoint debug** untuk memastikan data tersedia
3. **Pastikan format JSON** sesuai dengan contoh
4. **Cek authentication token** masih valid
5. **Pastikan PocketBase** server berjalan dengan baik
