const pb = require('../pocketbase/pbClient');
const { generateActivityCode, getNextActivitySequence } = require('../utils/idGenerator');

/**
 * Tambah aktivitas ke booking yang sudah ada
 */
exports.addActivityToBooking = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { activity, qty } = req.body;

    // Validasi input
    if (!activity || !qty) {
      return res.status(400).json({
        message: 'Activity dan QTY harus diisi'
      });
    }

    if (qty <= 0) {
      return res.status(400).json({
        message: 'Qty harus lebih dari 0'
      });
    }

    // Cek apakah booking exists - perbaikan: cek dua kemungkinan
    let bookingRecord = null;
    try {
      // Pertama coba cari berdasarkan ID record PocketBase
      try {
        bookingRecord = await pb.collection('booking_uab').getOne(bookingId);
        console.log('Booking found by ID:', bookingRecord.id);
      } catch (idError) {
        // Jika tidak ditemukan berdasarkan ID, coba cari berdasarkan field booking_id
        console.log('Booking not found by ID, trying by booking_id field...');
        bookingRecord = await pb.collection('booking_uab').getFirstListItem(`booking_id = "${bookingId}"`);
        console.log('Booking found by booking_id field:', bookingRecord.id);
      }
    } catch (error) {
      console.error('Booking not found error:', error);
      return res.status(404).json({
        message: 'Booking tidak ditemukan',
        booking_id: bookingId,
        error: error.message
      });
    }

    // Gunakan booking_id yang sebenarnya untuk pencarian activities
    const actualBookingId = bookingRecord.booking_id || bookingId;
    console.log('Using booking_id for activities:', actualBookingId);

    // Get next sequence number untuk booking ini
    const nextSequence = await getNextActivitySequence(pb, actualBookingId);
    const activityCode = generateActivityCode(nextSequence);

    // Simpan booking activity
    const bookingActivity = await pb.collection('booking_activities').create({
      code_activity: activityCode,
      booking_id: actualBookingId,
      activity: activity,
      qty: qty
    });

    res.status(201).json({
      message: 'Aktivitas berhasil ditambahkan ke booking',
      data: bookingActivity,
      booking_info: {
        record_id: bookingRecord.id,
        booking_id: actualBookingId
      }
    });

  } catch (err) {
    console.error('Error adding activity to booking:', err);
    res.status(500).json({
      message: "Gagal menambahkan aktivitas ke booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Tambah multiple aktivitas ke booking sekaligus
 */
exports.addMultipleActivitiesToBooking = async (req, res) => {
  try {
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('User from token:', req.user ? req.user.id : 'No user found');

    const { booking_id, activity } = req.body;

    // Validasi input
    if (!booking_id) {
      return res.status(400).json({
        message: 'booking_id harus diisi'
      });
    }

    if (!activity || !Array.isArray(activity) || activity.length === 0) {
      return res.status(400).json({
        message: 'activity harus berupa array dan tidak boleh kosong'
      });
    }

    // Validasi setiap activity
    for (let i = 0; i < activity.length; i++) {
      const act = activity[i];
      if (!act.id || !act.qty) {
        return res.status(400).json({
          message: `Activity index ${i}: id dan qty harus diisi`
        });
      }
      if (act.qty <= 0) {
        return res.status(400).json({
          message: `Activity index ${i}: qty harus lebih dari 0`
        });
      }
    }

    // Cek apakah booking exists - perbaikan: cek dua kemungkinan
    let bookingRecord = null;
    try {
      // Pertama coba cari berdasarkan ID record PocketBase
      try {
        bookingRecord = await pb.collection('booking_uab').getOne(booking_id);
        console.log('Booking found by ID:', bookingRecord.id);
      } catch (idError) {
        // Jika tidak ditemukan berdasarkan ID, coba cari berdasarkan field booking_id
        console.log('Booking not found by ID, trying by booking_id field...');
        bookingRecord = await pb.collection('booking_uab').getFirstListItem(`booking_id = "${booking_id}"`);
        console.log('Booking found by booking_id field:', bookingRecord.id);
      }
    } catch (error) {
      console.error('Booking not found error:', error);
      return res.status(404).json({
        message: 'Booking tidak ditemukan',
        booking_id: booking_id,
        error: error.message
      });
    }

    // Gunakan booking_id yang sebenarnya untuk pencarian activities
    const actualBookingId = bookingRecord.booking_id || booking_id;
    console.log('Using booking_id for activities:', actualBookingId);

    // Get next sequence number untuk booking ini
    let nextSequence = await getNextActivitySequence(pb, actualBookingId);

    // Simpan semua activities
    const savedActivities = [];
    for (const act of activity) {
      try {
        const activityCode = generateActivityCode(nextSequence);
        console.log(`Creating activity: ${activityCode}, booking_id: ${actualBookingId}, activity: ${act.id}, qty: ${act.qty}`);

        const bookingActivity = await pb.collection('booking_activities').create({
          code_activity: activityCode,
          booking_id: actualBookingId,
          activity: act.id,
          qty: act.qty
        });

        savedActivities.push(bookingActivity);
        nextSequence++;
        console.log(`Activity created successfully: ${activityCode}`);
      } catch (actError) {
        console.error(`Error creating activity ${act.id}:`, actError);
        throw new Error(`Gagal membuat aktivitas ${act.id}: ${actError.message}`);
      }
    }

    res.status(201).json({
      message: `${savedActivities.length} aktivitas berhasil ditambahkan ke booking`,
      data: savedActivities,
      booking_info: {
        record_id: bookingRecord.id,
        booking_id: actualBookingId
      }
    });

  } catch (err) {
    console.error('Error adding multiple activities to booking:', err);
    res.status(500).json({
      message: "Gagal menambahkan aktivitas ke booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Debug endpoint untuk melihat data yang tersedia
 */
exports.debugBookingData = async (req, res) => {
  console.log('=== DEBUG ENDPOINT CALLED ===');

  try {
    console.log('PocketBase URL:', pb.baseUrl);
    console.log('PocketBase instance exists:', !!pb);

    // Test koneksi sederhana dulu
    const testResponse = {
      message: 'Debug data berhasil diambil',
      timestamp: new Date().toISOString(),
      pb_info: {
        base_url: pb.baseUrl,
        instance_exists: !!pb
      },
      data: {
        recent_bookings: [],
        sample_products: [],
        recent_activities: [],
        errors: []
      }
    };

    // Test koneksi PocketBase dengan timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('PocketBase connection timeout')), 5000);
    });

    try {
      console.log('Testing PocketBase connection...');

      // Test dengan collection yang paling sederhana
      const healthCheck = Promise.race([
        pb.health.check(),
        timeoutPromise
      ]);

      await healthCheck;
      console.log('PocketBase health check passed');
      testResponse.pb_info.health_check = 'passed';

    } catch (healthError) {
      console.error('PocketBase health check failed:', healthError.message);
      testResponse.pb_info.health_check = 'failed';
      testResponse.pb_info.health_error = healthError.message;
    }

    // Coba ambil data dengan error handling yang lebih baik
    try {
      console.log('Fetching bookings...');
      const bookings = await Promise.race([
        pb.collection('booking_uab').getList(1, 3, { sort: '-created' }),
        timeoutPromise
      ]);

      testResponse.data.recent_bookings = bookings.items.map(b => ({
        id: b.id,
        booking_id: b.booking_id,
        nama: b.nama || 'N/A',
        created: b.created
      }));
      console.log('Bookings fetched successfully:', bookings.items.length);

    } catch (bookingError) {
      console.error('Booking fetch error:', bookingError.message);
      testResponse.data.errors.push({
        type: 'booking_error',
        message: bookingError.message
      });
    }

    try {
      console.log('Fetching products...');
      const products = await Promise.race([
        pb.collection('product_uab').getList(1, 3, { sort: 'nama_layanan' }),
        timeoutPromise
      ]);

      testResponse.data.sample_products = products.items.map(p => ({
        id: p.id,
        nama_layanan: p.nama_layanan || 'N/A',
        harga: p.harga || 0
      }));
      console.log('Products fetched successfully:', products.items.length);

    } catch (productError) {
      console.error('Product fetch error:', productError.message);
      testResponse.data.errors.push({
        type: 'product_error',
        message: productError.message
      });
    }

    try {
      console.log('Fetching activities...');
      const activities = await Promise.race([
        pb.collection('booking_activities').getList(1, 3, { sort: '-created' }),
        timeoutPromise
      ]);

      testResponse.data.recent_activities = activities.items.map(a => ({
        id: a.id,
        code_activity: a.code_activity || 'N/A',
        booking_id: a.booking_id || 'N/A',
        activity: a.activity || 'N/A',
        qty: a.qty || 0,
        created: a.created
      }));
      console.log('Activities fetched successfully:', activities.items.length);

    } catch (activityError) {
      console.error('Activity fetch error:', activityError.message);
      testResponse.data.errors.push({
        type: 'activity_error',
        message: activityError.message
      });
    }

    console.log('Debug response prepared, sending...');
    res.status(200).json(testResponse);

  } catch (err) {
    console.error('=== CRITICAL ERROR IN DEBUG ===');
    console.error('Error type:', err.constructor.name);
    console.error('Error message:', err.message);
    console.error('Error stack:', err.stack);

    res.status(500).json({
      message: "Gagal mengambil debug data",
      error: {
        type: err.constructor.name,
        message: err.message,
        stack: err.stack?.split('\n').slice(0, 5) // Limit stack trace
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get semua aktivitas untuk booking tertentu
 */
exports.getBookingActivities = async (req, res) => {
  try {
    const { bookingId } = req.params;

    // Cek apakah booking exists - perbaikan: cek dua kemungkinan
    let bookingRecord = null;
    try {
      // Pertama coba cari berdasarkan ID record PocketBase
      try {
        bookingRecord = await pb.collection('booking_uab').getOne(bookingId);
        console.log('Booking found by ID:', bookingRecord.id);
      } catch (idError) {
        // Jika tidak ditemukan berdasarkan ID, coba cari berdasarkan field booking_id
        console.log('Booking not found by ID, trying by booking_id field...');
        bookingRecord = await pb.collection('booking_uab').getFirstListItem(`booking_id = "${bookingId}"`);
        console.log('Booking found by booking_id field:', bookingRecord.id);
      }
    } catch (error) {
      console.error('Booking not found error:', error);
      return res.status(404).json({
        message: 'Booking tidak ditemukan',
        booking_id: bookingId,
        error: error.message
      });
    }

    // Gunakan booking_id yang sebenarnya untuk pencarian activities
    const actualBookingId = bookingRecord.booking_id || bookingId;
    console.log('Using booking_id for activities:', actualBookingId);

    // Get semua aktivitas untuk booking ini
    const activities = await pb.collection('booking_activities').getFullList({
      filter: `booking_id = "${actualBookingId}"`,
      sort: 'code_activity',
      expand: 'activity'
    });

    res.status(200).json({
      message: 'Data aktivitas booking berhasil diambil',
      data: activities,
      booking_info: {
        record_id: bookingRecord.id,
        booking_id: actualBookingId
      }
    });

  } catch (err) {
    console.error('Error getting booking activities:', err);
    res.status(500).json({
      message: "Gagal mengambil data aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Update aktivitas booking
 */
exports.updateBookingActivity = async (req, res) => {
  try {
    const { activityId } = req.params;
    const { activity, qty } = req.body;

    // Validasi input
    if (qty && qty <= 0) {
      return res.status(400).json({ 
        message: 'Qty harus lebih dari 0' 
      });
    }

    // Update data
    const updateData = {};
    if (activity) updateData.activity = activity;
    if (qty) updateData.qty = qty;

    const updatedActivity = await pb.collection('booking_activities').update(activityId, updateData);

    res.status(200).json({
      message: 'Aktivitas booking berhasil diupdate',
      data: updatedActivity
    });

  } catch (err) {
    console.error('Error updating booking activity:', err);
    if (err.status === 404) {
      return res.status(404).json({ 
        message: 'Aktivitas booking tidak ditemukan' 
      });
    }
    res.status(500).json({
      message: "Gagal mengupdate aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Hapus aktivitas booking
 */
exports.deleteBookingActivity = async (req, res) => {
  try {
    const { activityId } = req.params;

    await pb.collection('booking_activities').delete(activityId);

    res.status(200).json({
      message: 'Aktivitas booking berhasil dihapus'
    });

  } catch (err) {
    console.error('Error deleting booking activity:', err);
    if (err.status === 404) {
      return res.status(404).json({ 
        message: 'Aktivitas booking tidak ditemukan' 
      });
    }
    res.status(500).json({
      message: "Gagal menghapus aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};
