const pb = require('../pocketbase/pbClient');
const { generateActivityCode, getNextActivitySequence } = require('../utils/idGenerator');

/**
 * Tambah aktivitas ke booking yang sudah ada
 */
exports.addActivityToBooking = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { activity, qty } = req.body;

    // Validasi input
    if (!activity || !qty) {
      return res.status(400).json({
        message: 'Activity dan QTY harus diisi'
      });
    }

    if (qty <= 0) {
      return res.status(400).json({
        message: 'Qty harus lebih dari 0'
      });
    }

    // Cek apakah booking exists - perbaikan: cek dua kemungkinan
    let bookingRecord = null;
    try {
      // Pertama coba cari berdasarkan ID record PocketBase
      try {
        bookingRecord = await pb.collection('booking_uab').getOne(bookingId);
        console.log('Booking found by ID:', bookingRecord.id);
      } catch (idError) {
        // Jika tidak ditemukan berdasarkan ID, coba cari berdasarkan field booking_id
        console.log('Booking not found by ID, trying by booking_id field...');
        bookingRecord = await pb.collection('booking_uab').getFirstListItem(`booking_id = "${bookingId}"`);
        console.log('Booking found by booking_id field:', bookingRecord.id);
      }
    } catch (error) {
      console.error('Booking not found error:', error);
      return res.status(404).json({
        message: 'Booking tidak ditemukan',
        booking_id: bookingId,
        error: error.message
      });
    }

    // Gunakan booking_id yang sebenarnya untuk pencarian activities
    const actualBookingId = bookingRecord.booking_id || bookingId;
    console.log('Using booking_id for activities:', actualBookingId);

    // Get next sequence number untuk booking ini
    const nextSequence = await getNextActivitySequence(pb, actualBookingId);
    const activityCode = generateActivityCode(nextSequence);

    // Simpan booking activity
    const bookingActivity = await pb.collection('booking_activities').create({
      code_activity: activityCode,
      booking_id: actualBookingId,
      activity: activity,
      qty: qty
    });

    res.status(201).json({
      message: 'Aktivitas berhasil ditambahkan ke booking',
      data: bookingActivity,
      booking_info: {
        record_id: bookingRecord.id,
        booking_id: actualBookingId
      }
    });

  } catch (err) {
    console.error('Error adding activity to booking:', err);
    res.status(500).json({
      message: "Gagal menambahkan aktivitas ke booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Tambah multiple aktivitas ke booking sekaligus
 */
exports.addMultipleActivitiesToBooking = async (req, res) => {
  try {
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('User from token:', req.user ? req.user.id : 'No user found');

    const { booking_id, activity } = req.body;

    // Validasi input
    if (!booking_id) {
      return res.status(400).json({
        message: 'booking_id harus diisi'
      });
    }

    if (!activity || !Array.isArray(activity) || activity.length === 0) {
      return res.status(400).json({
        message: 'activity harus berupa array dan tidak boleh kosong'
      });
    }

    // Validasi setiap activity
    for (let i = 0; i < activity.length; i++) {
      const act = activity[i];
      if (!act.id || !act.qty) {
        return res.status(400).json({
          message: `Activity index ${i}: id dan qty harus diisi`
        });
      }
      if (act.qty <= 0) {
        return res.status(400).json({
          message: `Activity index ${i}: qty harus lebih dari 0`
        });
      }
    }

    // Cek apakah booking exists - perbaikan: cek dua kemungkinan
    let bookingRecord = null;
    try {
      // Pertama coba cari berdasarkan ID record PocketBase
      try {
        bookingRecord = await pb.collection('booking_uab').getOne(booking_id);
        console.log('Booking found by ID:', bookingRecord.id);
      } catch (idError) {
        // Jika tidak ditemukan berdasarkan ID, coba cari berdasarkan field booking_id
        console.log('Booking not found by ID, trying by booking_id field...');
        bookingRecord = await pb.collection('booking_uab').getFirstListItem(`booking_id = "${booking_id}"`);
        console.log('Booking found by booking_id field:', bookingRecord.id);
      }
    } catch (error) {
      console.error('Booking not found error:', error);
      return res.status(404).json({
        message: 'Booking tidak ditemukan',
        booking_id: booking_id,
        error: error.message
      });
    }

    // Gunakan booking_id yang sebenarnya untuk pencarian activities
    const actualBookingId = bookingRecord.booking_id || booking_id;
    console.log('Using booking_id for activities:', actualBookingId);

    // Get next sequence number untuk booking ini
    let nextSequence = await getNextActivitySequence(pb, actualBookingId);

    // Simpan semua activities
    const savedActivities = [];
    for (const act of activity) {
      try {
        const activityCode = generateActivityCode(nextSequence);
        console.log(`Creating activity: ${activityCode}, booking_id: ${actualBookingId}, activity: ${act.id}, qty: ${act.qty}`);

        const bookingActivity = await pb.collection('booking_activities').create({
          code_activity: activityCode,
          booking_id: actualBookingId,
          activity: act.id,
          qty: act.qty
        });

        savedActivities.push(bookingActivity);
        nextSequence++;
        console.log(`Activity created successfully: ${activityCode}`);
      } catch (actError) {
        console.error(`Error creating activity ${act.id}:`, actError);
        throw new Error(`Gagal membuat aktivitas ${act.id}: ${actError.message}`);
      }
    }

    res.status(201).json({
      message: `${savedActivities.length} aktivitas berhasil ditambahkan ke booking`,
      data: savedActivities,
      booking_info: {
        record_id: bookingRecord.id,
        booking_id: actualBookingId
      }
    });

  } catch (err) {
    console.error('Error adding multiple activities to booking:', err);
    res.status(500).json({
      message: "Gagal menambahkan aktivitas ke booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Debug endpoint untuk melihat data yang tersedia
 */
exports.debugBookingData = async (req, res) => {
  try {
    // Get semua booking
    const bookings = await pb.collection('booking_uab').getList(1, 5, {
      sort: '-created'
    });

    // Get semua products
    const products = await pb.collection('product_uab').getList(1, 5, {
      sort: 'nama_layanan'
    });

    res.status(200).json({
      message: 'Debug data berhasil diambil',
      data: {
        recent_bookings: bookings.items.map(b => ({
          id: b.id,
          booking_id: b.booking_id,
          nama: b.nama,
          created: b.created
        })),
        sample_products: products.items.map(p => ({
          id: p.id,
          nama_layanan: p.nama_layanan,
          harga: p.harga
        }))
      }
    });

  } catch (err) {
    console.error('Error getting debug data:', err);
    res.status(500).json({
      message: "Gagal mengambil debug data",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Get semua aktivitas untuk booking tertentu
 */
exports.getBookingActivities = async (req, res) => {
  try {
    const { bookingId } = req.params;

    // Cek apakah booking exists - perbaikan: cek dua kemungkinan
    let bookingRecord = null;
    try {
      // Pertama coba cari berdasarkan ID record PocketBase
      try {
        bookingRecord = await pb.collection('booking_uab').getOne(bookingId);
        console.log('Booking found by ID:', bookingRecord.id);
      } catch (idError) {
        // Jika tidak ditemukan berdasarkan ID, coba cari berdasarkan field booking_id
        console.log('Booking not found by ID, trying by booking_id field...');
        bookingRecord = await pb.collection('booking_uab').getFirstListItem(`booking_id = "${bookingId}"`);
        console.log('Booking found by booking_id field:', bookingRecord.id);
      }
    } catch (error) {
      console.error('Booking not found error:', error);
      return res.status(404).json({
        message: 'Booking tidak ditemukan',
        booking_id: bookingId,
        error: error.message
      });
    }

    // Gunakan booking_id yang sebenarnya untuk pencarian activities
    const actualBookingId = bookingRecord.booking_id || bookingId;
    console.log('Using booking_id for activities:', actualBookingId);

    // Get semua aktivitas untuk booking ini
    const activities = await pb.collection('booking_activities').getFullList({
      filter: `booking_id = "${actualBookingId}"`,
      sort: 'code_activity',
      expand: 'activity'
    });

    res.status(200).json({
      message: 'Data aktivitas booking berhasil diambil',
      data: activities,
      booking_info: {
        record_id: bookingRecord.id,
        booking_id: actualBookingId
      }
    });

  } catch (err) {
    console.error('Error getting booking activities:', err);
    res.status(500).json({
      message: "Gagal mengambil data aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Update aktivitas booking
 */
exports.updateBookingActivity = async (req, res) => {
  try {
    const { activityId } = req.params;
    const { activity, qty } = req.body;

    // Validasi input
    if (qty && qty <= 0) {
      return res.status(400).json({ 
        message: 'Qty harus lebih dari 0' 
      });
    }

    // Update data
    const updateData = {};
    if (activity) updateData.activity = activity;
    if (qty) updateData.qty = qty;

    const updatedActivity = await pb.collection('booking_activities').update(activityId, updateData);

    res.status(200).json({
      message: 'Aktivitas booking berhasil diupdate',
      data: updatedActivity
    });

  } catch (err) {
    console.error('Error updating booking activity:', err);
    if (err.status === 404) {
      return res.status(404).json({ 
        message: 'Aktivitas booking tidak ditemukan' 
      });
    }
    res.status(500).json({
      message: "Gagal mengupdate aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Hapus aktivitas booking
 */
exports.deleteBookingActivity = async (req, res) => {
  try {
    const { activityId } = req.params;

    await pb.collection('booking_activities').delete(activityId);

    res.status(200).json({
      message: 'Aktivitas booking berhasil dihapus'
    });

  } catch (err) {
    console.error('Error deleting booking activity:', err);
    if (err.status === 404) {
      return res.status(404).json({ 
        message: 'Aktivitas booking tidak ditemukan' 
      });
    }
    res.status(500).json({
      message: "Gagal menghapus aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};
