const fetch = require('node-fetch');

async function testMultipleActivities() {
  try {
    // Test data - ganti dengan data yang valid
    const testData = {
      "booking_id": "20250803+07vbixab", // Ganti dengan booking_id yang valid
      "activity": [
        {
          "id": "jya23u4o25swg5p", // Ganti dengan product_id yang valid
          "qty": 2
        }
      ]
    };

    console.log('Testing with data:', JSON.stringify(testData, null, 2));

    const response = await fetch('http://localhost:3000/booking_activities', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': 'Bearer YOUR_TOKEN_HERE' // Uncomment dan ganti dengan token yang valid
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response:', JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('Test error:', error);
  }
}

testMultipleActivities();
